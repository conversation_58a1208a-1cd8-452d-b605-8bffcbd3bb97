{"extension": {"name": "Roo Code", "description": "Cały zespół programistów AI w Twoim edytorze."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON><PERSON>, {{name}}! <PERSON><PERSON> {{count}} powiadomień.", "items": {"zero": "Brak elementów", "one": "Jeden element", "other": "{{count}} elementów"}, "confirmation": {"reset_state": "Czy na pewno chcesz zresetować wszystkie stany i tajne magazyny w rozszerzeniu? Tej operacji nie można cofnąć.", "delete_config_profile": "<PERSON>zy na pewno chcesz usunąć ten profil konfiguracyjny?", "delete_custom_mode": "<PERSON>zy na pewno chcesz usunąć ten niestandardowy tryb?", "delete_message": "Co chcesz usunąć?", "just_this_message": "<PERSON><PERSON><PERSON> tę wiadom<PERSON>", "this_and_subsequent": "Tę i wszystkie kolejne wiadomości"}, "errors": {"invalid_mcp_config": "Nieprawidłowy format konfiguracji MCP projektu", "invalid_mcp_settings_format": "Nieprawidłowy format JSON ustawień MCP. Upewnij się, że Twoje ustawienia są zgodne z poprawnym formatem JSON.", "invalid_mcp_settings_syntax": "Nieprawidłowy format JSON ustawień MCP. Sprawdź, czy w pliku ustawień nie ma błędów składniowych.", "invalid_mcp_settings_validation": "Nieprawidłowy format ustawień MCP: {{errorMessages}}", "failed_initialize_project_mcp": "<PERSON><PERSON> u<PERSON>ło się z<PERSON> serwera MCP projektu: {{error}}", "invalid_data_uri": "Nieprawidłowy format URI danych", "checkpoint_timeout": "Upłynął limit czasu podczas próby przywrócenia punktu kontrolnego.", "checkpoint_failed": "Nie udało się przywrócić punktu kontrolnego.", "no_workspace": "Najpierw otwórz folder projektu", "update_support_prompt": "Nie udało się zaktualizować komunikatu wsparcia", "reset_support_prompt": "<PERSON>e udało się zresetować komunikatu wsparcia", "enhance_prompt": "<PERSON>e udało się ulepszyć komunikatu", "get_system_prompt": "Nie udało się pobrać komunikatu systemowego", "search_commits": "<PERSON>e udało się wyszukać commitów", "save_api_config": "Nie udało się zapisać konfiguracji API", "create_api_config": "Nie udało się utworzyć konfiguracji API", "rename_api_config": "Nie udało się zmienić nazwy konfiguracji API", "load_api_config": "Nie udało się załadować konfiguracji API", "delete_api_config": "<PERSON>e udało się usunąć konfiguracji API", "list_api_config": "Nie udało się pobrać listy konfiguracji API", "update_server_timeout": "<PERSON>e udało się zaktualizować limitu czasu serwera", "failed_update_project_mcp": "Nie udało się zaktualizować serwerów MCP projektu", "create_mcp_json": "<PERSON>e udało się utworzyć lub otworzyć .roo/mcp.json: {{error}}", "hmr_not_running": "Lokalny serwer deweloperski nie jest urucho<PERSON>ny, HMR nie bę<PERSON><PERSON>. Uruchom 'npm run dev' przed uruchomieniem rozszerzenia, aby włączyć HMR.", "retrieve_current_mode": "Błąd podczas pobierania bieżącego trybu ze stanu.", "failed_delete_repo": "Nie udało się usunąć powiązanego repozytorium lub gałęzi pomocniczej: {{error}}", "failed_remove_directory": "<PERSON><PERSON> udało się usunąć katalogu zadania: {{error}}", "custom_storage_path_unusable": "Niestandardowa ścieżka przechowywania \"{{path}}\" nie jest u<PERSON>, zostanie użyta domyślna ścieżka", "cannot_access_path": "<PERSON>e można uzyskać dostępu do ścieżki {{path}}: {{error}}", "settings_import_failed": "<PERSON><PERSON> udało się zaimportować ustawień: {{error}}.", "mistake_limit_guidance": "To może wskazywać na błąd w procesie myślowym modelu lub niezdoln<PERSON> do prawidłowego użycia narzędzia, co można złagodzić poprzez wskazówki użytkownika (np. \"Spróbuj podzielić zadanie na mniejsze kroki\").", "violated_organization_allowlist": "Nie udało się uruchomić zadania: bieżący profil narusza ustawienia Twojej organizacji", "condense_failed": "<PERSON><PERSON> udało się skondensować kontekstu", "condense_not_enough_messages": "Za mało wiadomości do skondensowania kontekstu", "condensed_recently": "Kontekst został niedawno skondensowany; pomijanie tej próby", "condense_handler_invalid": "Nieprawidłowy handler API do kondensowania kontekstu", "condense_context_grew": "Rozmiar kontekstu wzrósł podczas kondensacji; pomijanie tej próby"}, "warnings": {"no_terminal_content": "Nie wybrano zawartości terminala", "missing_task_files": "Pliki tego zadania są brakujące. <PERSON><PERSON> ch<PERSON> usunąć je z listy zadań?"}, "info": {"no_changes": "Nie znaleziono zmian.", "clipboard_copy": "Komunikat systemowy został pomyślnie skopiowany do schowka", "history_cleanup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} zadań z brakującymi plikami z historii.", "mcp_server_restarting": "Ponowne uruchamianie serwera MCP {{serverName}}...", "mcp_server_connected": "Serwer MCP {{serverName}} połączony", "mcp_server_deleted": "Usunięto serwer MCP: {{serverName}}", "mcp_server_not_found": "<PERSON><PERSON> \"{{server<PERSON>ame}}\" nie znaleziony w konfiguracji", "custom_storage_path_set": "Ustawiono niestandardową ścieżkę przechowywania: {{path}}", "default_storage_path": "Wznowiono używanie domyślnej ścieżki przechowywania", "settings_imported": "Ustawienia zaimportowane pomyślnie."}, "answers": {"yes": "Tak", "no": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Usuń", "keep": "<PERSON><PERSON><PERSON>"}, "tasks": {"canceled": "Błąd zadania: Zostało zatrzymane i anulowane przez użytkownika.", "deleted": "Niepowodzenie zadania: Zostało zatrzymane i usunięte przez użytkownika."}, "storage": {"prompt_custom_path": "Wprowadź niestandardową ścieżkę przechowywania dla historii konwersacji lub pozostaw puste, aby użyć lokalizacji domyślnej", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Wprowadź pełną ścieżkę (np. D:\\RooCodeStorage lub /home/<USER>/storage)", "enter_valid_path": "Wprowadź prawidłową ścieżkę"}, "input": {"task_prompt": "Co ma zrobić Roo?", "task_placeholder": "Wpisz swoje zadanie tutaj"}, "settings": {"providers": {"groqApiKey": "Klucz API Groq", "getGroqApiKey": "Uzyskaj klucz API Groq"}}}