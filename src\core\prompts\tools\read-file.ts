import { ToolArgs } from "./types"

export function getReadFileDescription(args: ToolArgs): string {
	// Base description without partial read instructions
	let description = `## read_file
Description: Request to read the contents of a file at the specified path. Use this when you need to examine the contents of an existing file you do not know the contents of, for example to analyze code, review text files, or extract information from configuration files. The output includes line numbers prefixed to each line (e.g. "1 | const x = 1"), making it easier to reference specific lines when creating diffs or discussing code.`

	// Add partial read instructions only when partial reads are active
	if (args.partialReadsEnabled) {
		description += ` By specifying start_line and end_line parameters, you can efficiently read specific portions of large files without loading the entire file into memory.`
	}

	description += ` Automatically extracts raw text from PDF and DOCX files. May not be suitable for other types of binary files, as it returns the raw content as a string.
Parameters:
- path: (required) The path of the file to read (relative to the current workspace directory ${args.cwd})`

	// Add start_line and end_line parameters only when partial reads are active
	if (args.partialReadsEnabled) {
		description += `
- start_line: (optional) The starting line number to read from (1-based). If not provided, it starts from the beginning of the file.
- end_line: (optional) The ending line number to read to (1-based, inclusive). If not provided, it reads to the end of the file.`
	}

	description += `
Usage:
<read_file>
<path>File path here</path>`

	// Add start_line and end_line in usage only when partial reads are active
	if (args.partialReadsEnabled) {
		description += `
<start_line>Starting line number (optional)</start_line>
<end_line>Ending line number (optional)</end_line>`
	}

	description += `
</read_file>

Examples:

1. Reading an entire file:
<read_file>
<path>frontend-config.json</path>
</read_file>`

	// Add partial read examples only when partial reads are active
	if (args.partialReadsEnabled) {
		description += `

2. Reading the first 1000 lines of a large log file:
<read_file>
<path>logs/application.log</path>
<end_line>1000</end_line>
</read_file>

3. Reading lines 500-1000 of a CSV file:
<read_file>
<path>data/large-dataset.csv</path>
<start_line>500</start_line>
<end_line>1000</end_line>
</read_file>

4. Reading a specific function in a source file:
<read_file>
<path>src/app.ts</path>
<start_line>46</start_line>
<end_line>68</end_line>
</read_file>

Note: When both start_line and end_line are provided, this tool efficiently streams only the requested lines, making it suitable for processing large files like logs, CSV files, and other large datasets without memory issues.`
	}

	return description
}
